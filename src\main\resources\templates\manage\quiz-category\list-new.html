<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}" xmlns:sec="http://www.thymeleaf.org/extras/spring-security">
<head>
    <title>문제 카테고리 관리 (신규) - 한림공원 관리시스템</title>
    <meta name="description" content="문제 카테고리를 관리합니다.">
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/common/admin-list.css" rel="stylesheet">
    <link href="/css/manage/content/quiz-category.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>콘텐츠 카테고리 관리 (신규)</h2>
                        <p>문제의 카테고리를 관리합니다. (공통 기능 적용)</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/quiz-category/new}" class="btn btn-primary">
                            새 카테고리 등록
                        </a>
                        <a th:href="@{/manage/quiz-category}" class="btn btn-outline-secondary">
                            기존 페이지로
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 공통 검색/필터/정렬 컨트롤 -->
                <div th:replace="fragments/admin-list-controls :: controls(
                    searchFields='category_name:카테고리명,description:설명',
                    filterOptions='',
                    sortOptions='category_id:ID,category_name:카테고리명,create_date:등록일,last_update_date:수정일',
                    searchPlaceholder='카테고리명 또는 설명으로 검색',
                    showDateRange=false,
                    showNumberRange=false
                )"></div>
                
                <!-- 동적 테이블 컨테이너 -->
                <div th:replace="fragments/admin-list-controls :: table-container"></div>
                
                <!-- 페이징 네비게이션 -->
                <div th:replace="fragments/admin-list-controls :: pagination(${listResponse})"></div>
            </div>
        </section>
    </div>

    <!-- 추가 스크립트 -->
    <th:block layout:fragment="script">
        <script src="/js/manage/admin-list-manager.js"></script>
        <script th:inline="javascript">
            // 페이지별 설정
            const pageConfig = {
                apiEndpoint: '/manage/quiz-category/search',
                customRowRenderer: function(items, response) {
                    if (!items || items.length === 0) {
                        return `
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <h5 class="text-muted">등록된 문제 카테고리가 없습니다</h5>
                                <p class="text-muted">새로운 문제 카테고리를 등록해보세요.</p>
                                <a href="/manage/quiz-category/new" class="btn btn-primary">
                                    첫 번째 카테고리 등록하기
                                </a>
                            </div>
                        `;
                    }
                    
                    let html = `
                        <table class="table table-hover">
                            <thead class="table-light">
                                <tr>
                                    <th scope="col" style="width: 80px;">ID</th>
                                    <th scope="col" style="width: 200px;">카테고리명</th>
                                    <th scope="col">설명</th>
                                    <th scope="col" style="width: 120px;">관리</th>
                                </tr>
                            </thead>
                            <tbody>
                    `;
                    
                    items.forEach(category => {
                        html += `
                            <tr data-quiz-category-id="${category.categoryId}">
                                <td>
                                    <span class="badge bg-secondary">${category.categoryId}</span>
                                </td>
                                <td>
                                    <strong>${category.categoryName}</strong>
                                </td>
                                <td>
                                    <span class="text-muted">${category.description || '설명이 없습니다.'}</span>
                                </td>
                                <td>
                                    <div class="btn-group" role="group">
                                        <a href="/manage/quiz-category/${category.categoryId}/edit" 
                                           class="btn btn-outline-primary btn-sm">
                                            수정
                                        </a>
                                        <button class="btn btn-outline-danger btn-sm"
                                                data-quiz-category-id="${category.categoryId}"
                                                data-quiz-category-name="${category.categoryName}"
                                                onclick="deleteCategory(this)">
                                            삭제
                                        </button>
                                    </div>
                                </td>
                            </tr>
                        `;
                    });
                    
                    html += `
                            </tbody>
                        </table>
                    `;
                    
                    return html;
                },
                onDataLoaded: function(response) {
                    console.log('Data loaded:', response);
                },
                onError: function(error) {
                    console.error('Error occurred:', error);
                }
            };
            
            // AdminListManager 초기화
            const listManager = new AdminListManager(pageConfig);
            
            // 카테고리 삭제 함수
            function deleteCategory(button) {
                const quizCategoryId = button.getAttribute('data-quiz-category-id');
                const quizCategoryName = button.getAttribute('data-quiz-category-name');

                if (!confirm(`'${quizCategoryName}' 카테고리를 정말 삭제하시겠습니까?\n\n이 작업은 되돌릴 수 없습니다.`)) {
                    return;
                }

                // 버튼 비활성화
                button.disabled = true;
                button.textContent = '삭제 중...';

                // AJAX 요청
                $.ajax({
                    url: `/manage/quiz-category/${quizCategoryId}`,
                    type: 'DELETE',
                    beforeSend: function (xhr) { 
                        const token = $('meta[name="_csrf"]').attr('content');
                        const header = $('meta[name="_csrf_header"]').attr('content');
                        if (token && header) {
                            xhr.setRequestHeader(header, token);
                        }
                    },
                    success: function(response) {
                        if (response.success) {
                            alert(response.message || '카테고리가 성공적으로 삭제되었습니다.');
                            // 리스트 새로고침
                            listManager.refresh();
                        } else {
                            alert('삭제 실패: ' + (response.message || '알 수 없는 오류가 발생했습니다.'));
                            // 버튼 복원
                            button.disabled = false;
                            button.textContent = '삭제';
                        }
                    },
                    error: function(xhr, status, error) {
                        console.error('Delete error:', error);
                        let errorMessage = '카테고리 삭제 중 오류가 발생했습니다.';

                        if (xhr.responseJSON && xhr.responseJSON.message) {
                            errorMessage = xhr.responseJSON.message;
                        }

                        alert('삭제 실패: ' + errorMessage);

                        // 버튼 복원
                        button.disabled = false;
                        button.textContent = '삭제';
                    }
                });
            }
        </script>
    </th:block>
</body>
</html>
