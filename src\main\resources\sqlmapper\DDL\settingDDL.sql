-- 1. 카테고리 테이블 (quiz_category)
CREATE TABLE `quiz_category` (
    `category_id`       BIGINT          NOT NULL AUTO_INCREMENT COMMENT '카테고리 고유 ID',
    `category_name`     VARCHAR(200)    NOT NULL COMMENT '카테고리명',
    `description`       TEXT            NULL COMMENT '카테고리 설명',
    `create_id`         VARCHAR(50)     NULL COMMENT '생성자 (user_email)',
    `create_date`       DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시',
    `last_update_id`    VARCHAR(50)     NULL COMMENT '최종수정자 (user_email)',
    `last_update_date`  DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '최종수정일시',
    `delete_yn`         ENUM('Y','N')   NOT NULL DEFAULT 'N' COMMENT '삭제여부',
    `delete_id`         VARCHAR(50)     NULL COMMENT '삭제자 (user_email)',
    `delete_date`       DATETIME        NULL COMMENT '삭제일시',
    
    PRIMARY KEY (`category_id`),
    UNIQUE KEY `UX_category_name` (`category_name`)
) COMMENT '문제 카테고리 정보';


-- 2. 문제 마스터 테이블 (quiz_master)
CREATE TABLE `quiz_master` (
    `quiz_id`           BIGINT          NOT NULL AUTO_INCREMENT COMMENT '문제 고유 ID',
    `category_id`       BIGINT          NOT NULL COMMENT '카테고리 ID (FK)',
    `quiz_type`         VARCHAR(20)     NOT NULL COMMENT '문제 유형 (MCQ, OX, ORDER 등)',
    `difficulty_level`  INT             NOT NULL DEFAULT 1 COMMENT '난이도 (1~5)',
    `correct_answer`    TEXT            NOT NULL COMMENT '정답 (JSON, 텍스트 등)',
    `image_url`         VARCHAR(255)    NULL COMMENT '문제 이미지 경로',
    `hint`              TEXT            NULL COMMENT '힌트 내용',
    `create_id`         VARCHAR(50)     NULL COMMENT '생성자 (user_email)',
    `create_date`       DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시',
    `last_update_id`    VARCHAR(50)     NULL COMMENT '최종수정자 (user_email)',
    `last_update_date`  DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '최종수정일시',
    `delete_yn`         ENUM('Y','N')   NOT NULL DEFAULT 'N' COMMENT '삭제여부',
    `delete_id`         VARCHAR(50)     NULL COMMENT '삭제자 (user_email)',
    `delete_date`       DATETIME        NULL COMMENT '삭제일시',
    
    PRIMARY KEY (`quiz_id`),
    FOREIGN KEY (`category_id`) REFERENCES `quiz_category` (`category_id`)
) COMMENT '문제 마스터 정보';


-- 3. 문제 콘텐츠 테이블 (quiz_content)
CREATE TABLE `quiz_content` (
    `quiz_content_id`   BIGINT          NOT NULL AUTO_INCREMENT COMMENT '콘텐츠 고유 ID',
    `quiz_id`           BIGINT          NOT NULL COMMENT '문제 ID (FK)',
    `lang_code`         VARCHAR(10)     NOT NULL COMMENT '언어 코드 (ko, en, zh, ja)',
    `question`          TEXT            NOT NULL COMMENT '문제 질문 내용',
    `options`           JSON            NULL COMMENT '문제 보기 (객관식 등에서 사용)',
    `create_id`         VARCHAR(50)     NULL COMMENT '생성자 (user_email)',
    `create_date`       DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시',
    `last_update_id`    VARCHAR(50)     NULL COMMENT '최종수정자 (user_email)',
    `last_update_date`  DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '최종수정일시',
    `delete_yn`         ENUM('Y','N')   NOT NULL DEFAULT 'N' COMMENT '삭제여부',
    `delete_id`         VARCHAR(50)     NULL COMMENT '삭제자 (user_email)',
    `delete_date`       DATETIME        NULL COMMENT '삭제일시',

    PRIMARY KEY (`quiz_content_id`),
    UNIQUE KEY `UX_quiz_id_lang_code` (`quiz_id`, `lang_code`),
    FOREIGN KEY (`quiz_id`) REFERENCES `quiz_master` (`quiz_id`) ON DELETE CASCADE
) COMMENT '문제 다국어 콘텐츠 정보';


-- 4. QR-문제 매핑 테이블 (qr_quiz_mapping)
CREATE TABLE `qr_quiz_mapping` (
    `mapping_id`        BIGINT          NOT NULL AUTO_INCREMENT COMMENT '매핑 고유 ID',
    `qr_id`             VARCHAR(100)    NOT NULL COMMENT 'QR 코드 고유 식별자',
    `quiz_id`           BIGINT          NOT NULL COMMENT '문제 ID (FK)',
    `display_order`     INT             NULL COMMENT '스토리형 콘텐츠 문제 노출 순서',
    `create_id`         VARCHAR(50)     NULL COMMENT '생성자 (user_email)',
    `create_date`       DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시',
    `last_update_id`    VARCHAR(50)     NULL COMMENT '최종수정자 (user_email)',
    `last_update_date`  DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP COMMENT '최종수정일시',
    `delete_yn`         ENUM('Y','N')   NOT NULL DEFAULT 'N' COMMENT '삭제여부',
    `delete_id`         VARCHAR(50)     NULL COMMENT '삭제자 (user_email)',
    `delete_date`       DATETIME        NULL COMMENT '삭제일시',

    PRIMARY KEY (`mapping_id`),
    INDEX `IX_qr_id` (`qr_id`),
    FOREIGN KEY (`quiz_id`) REFERENCES `quiz_master` (`quiz_id`)
) COMMENT 'QR코드와 문제 매핑 정보';


-- 5. 사용자 참여 이력 테이블 (user_attempt)
CREATE TABLE `user_attempt` (
    `attempt_id`        BIGINT          NOT NULL AUTO_INCREMENT COMMENT '참여 이력 고유 ID',
    `user_idx`          BIGINT UNSIGNED NOT NULL COMMENT '참여한 사용자 고유 번호 (FK)',
    `quiz_id`           BIGINT          NOT NULL COMMENT '푼 문제 ID (FK)',
    `submitted_answer`  TEXT            NOT NULL COMMENT '사용자가 제출한 답',
    `is_correct`        BOOLEAN         NOT NULL COMMENT '정답 여부',
    `attempt_date`      DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '문제 풀이 시각',
    `create_id`         VARCHAR(50)     NULL COMMENT '생성자 (user_email)',
    `create_date`       DATETIME        NOT NULL DEFAULT CURRENT_TIMESTAMP COMMENT '생성일시',

    PRIMARY KEY (`attempt_id`),
    INDEX `IX_user_idx` (`user_idx`),
    FOREIGN KEY (`user_idx`) REFERENCES `users` (`user_idx`),
    FOREIGN KEY (`quiz_id`) REFERENCES `quiz_master` (`quiz_id`)
) COMMENT '사용자 문제 풀이 이력';

CREATE TABLE `users` (
  `user_idx` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '사용자 내부 고유 번호 (PK, 시스템 내부 식별용)',
  `user_email` varchar(255) NOT NULL COMMENT '사용자 아이디 (로그인용 이메일, 외부 테이블 참조용 키)',
  `name` varchar(100) NOT NULL COMMENT '사용자 이름',
  `password` varchar(255) NOT NULL COMMENT '비밀번호 (해싱됨)',
  `description` text DEFAULT NULL COMMENT '사용자 설명',
  `contact_number` varchar(50) DEFAULT NULL COMMENT '연락처',
  `status` enum('ACTIVE','INACTIVE','INVITED','LOCKED') NOT NULL DEFAULT 'ACTIVE' COMMENT '계정 상태',
  `user_role` varchar(12) NOT NULL DEFAULT 'USER' COMMENT '사용자 권한',
  `last_login_date` datetime DEFAULT NULL COMMENT '최종 로그인 일시',
  `create_user_email` varchar(255) DEFAULT NULL COMMENT '생성자 user_email (FK)',
  `create_date` datetime DEFAULT current_timestamp() COMMENT '생성 시기',
  `update_user_email` varchar(255) DEFAULT NULL COMMENT '수정자 user_email (FK)',
  `last_update_date` datetime DEFAULT current_timestamp() ON UPDATE current_timestamp() COMMENT '수정 시기',
  `delete_user_email` varchar(255) DEFAULT NULL COMMENT '삭제자 user_email (FK)',
  `delete_date` datetime DEFAULT NULL COMMENT '삭제 일시',
  `use_yn` enum('Y','N') NOT NULL DEFAULT 'Y' COMMENT '사용 여부',
  `delete_yn` enum('Y','N') NOT NULL DEFAULT 'N' COMMENT '삭제 여부',
  `login_fail_count` int(11) DEFAULT 0 COMMENT '로그인 실패 횟수',
  `user_token_id` varchar(50) DEFAULT NULL COMMENT '사용자 고유 토큰값',
  PRIMARY KEY (`user_idx`),
  UNIQUE KEY `user_email` (`user_email`),
  KEY `create_user_email` (`create_user_email`) USING BTREE,
  KEY `delete_user_email` (`delete_user_email`) USING BTREE,
  KEY `idx_users_delete_yn` (`delete_yn`) USING BTREE,
  KEY `idx_users_status` (`status`) USING BTREE,
  KEY `update_user_email` (`update_user_email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=3 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='시스템 사용자 (관리자, 등록 사용자 포함)';

CREATE TABLE `user_login_attempt_log` (
  `log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '로그 번호',
  `user_email` varchar(50) NOT NULL COMMENT '로그인 시도 아이디',
  `login_type` varchar(50) NOT NULL DEFAULT 'password' COMMENT '로그인 인증 방법',
  `attempt_ip` varchar(32) NOT NULL COMMENT '로그인 시도 IP',
  `attempt_agent` varchar(500) DEFAULT NULL COMMENT '로그인 시도 USER AGENT',
  `attempt_referer` varchar(500) DEFAULT NULL COMMENT '로그인 시도 REFERER',
  `attempt_time` datetime NOT NULL COMMENT '로그인 시도 시간',
  `error_code` varchar(10) NOT NULL COMMENT '로그인 에러 코드',
  `error_message` varchar(500) NOT NULL COMMENT '로그인 에러 메시지',
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=47 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci;

CREATE TABLE `user_login_session` (
  `seq` int(10) unsigned NOT NULL AUTO_INCREMENT COMMENT '고유번호',
  `user_email` varchar(50) DEFAULT NULL COMMENT '로그인 아이디',
  `login_type` varchar(50) NOT NULL DEFAULT 'password' COMMENT '로그인 인증방법',
  `login_session` varchar(100) NOT NULL COMMENT '로그인 세션아이디',
  `login_ip` varchar(32) NOT NULL COMMENT '로그인 아이피',
  `login_agent` varchar(500) DEFAULT NULL COMMENT '로그인 에이전트',
  `login_referer` varchar(500) DEFAULT NULL COMMENT '접근페이지',
  `login_time` datetime NOT NULL COMMENT '로그인 시간',
  `logout_time` datetime DEFAULT NULL COMMENT '로그아웃 시간',
  `logout_type` varchar(20) DEFAULT NULL COMMENT '로그아웃 방법',
  PRIMARY KEY (`seq`),
  KEY `user_login_session_fk` (`user_email`) USING BTREE
) ENGINE=InnoDB AUTO_INCREMENT=9 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='사용자 로그인 기록';

CREATE TABLE `webservice_log` (
  `log_id` bigint(20) unsigned NOT NULL AUTO_INCREMENT COMMENT '로그 번호',
  `user_token` varchar(50) NOT NULL COMMENT '사용자 아이디 token',
  `user_email` varchar(50) DEFAULT NULL COMMENT '사용자 이메일ID',
  `referer` varchar(500) DEFAULT NULL COMMENT '이전 페이지',
  `request_uri` varchar(200) NOT NULL COMMENT '요청 페이지',
  `request_params` text DEFAULT NULL COMMENT '요청 파라미터',
  `request_time` datetime NOT NULL COMMENT '요청 시간',
  `request_host` varchar(32) NOT NULL COMMENT '요청 호스트',
  `request_agent` varchar(500) DEFAULT NULL COMMENT '요청 Agent',
  `response_status` smallint(6) DEFAULT NULL COMMENT 'HTTP 응답코드',
  `session_id` varchar(32) NOT NULL COMMENT '접속 세션아이디',
  `tracking` enum('Y','N') NOT NULL DEFAULT 'N',
  PRIMARY KEY (`log_id`)
) ENGINE=InnoDB AUTO_INCREMENT=247 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_unicode_ci COMMENT='웹 이용 기록';