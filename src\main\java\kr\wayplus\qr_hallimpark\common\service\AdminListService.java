package kr.wayplus.qr_hallimpark.common.service;

import kr.wayplus.qr_hallimpark.common.dto.AdminListResponseDto;
import kr.wayplus.qr_hallimpark.common.dto.AdminListSearchDto;

/**
 * 관리자 리스트 페이지 공통 서비스 인터페이스
 * - 모든 관리자 리스트 페이지에서 구현해야 할 공통 메서드 정의
 * - 검색, 필터링, 정렬, 페이징 기능의 표준화
 */
public interface AdminListService<T> {
    
    /**
     * 검색 조건에 따른 리스트 조회
     * @param searchDto 검색 조건
     * @return 페이징된 리스트 응답
     */
    AdminListResponseDto<T> findListWithConditions(AdminListSearchDto searchDto);
    
    /**
     * 전체 데이터 개수 조회 (검색 조건 적용)
     * @param searchDto 검색 조건
     * @return 전체 데이터 개수
     */
    Long countWithConditions(AdminListSearchDto searchDto);
    
    /**
     * 기본 검색 조건 생성
     * - 각 서비스에서 기본값을 설정할 수 있도록 제공
     * @return 기본 검색 조건
     */
    default AdminListSearchDto createDefaultSearchCondition() {
        return AdminListSearchDto.builder()
                .page(1)
                .size(20)
                .sortField("create_date")
                .sortDirection("DESC")
                .baseCondition("delete_yn = 'N'")
                .build();
    }
    
    /**
     * 검색 조건 유효성 검사
     * @param searchDto 검색 조건
     * @throws IllegalArgumentException 유효하지 않은 검색 조건인 경우
     */
    default void validateSearchCondition(AdminListSearchDto searchDto) {
        if (searchDto == null) {
            throw new IllegalArgumentException("검색 조건이 null입니다.");
        }
        
        if (searchDto.getPage() == null || searchDto.getPage() < 1) {
            searchDto.setPage(1);
        }
        
        if (searchDto.getSize() == null || searchDto.getSize() < 1 || searchDto.getSize() > 100) {
            searchDto.setSize(20);
        }
        
        if (searchDto.getSortField() == null || searchDto.getSortField().trim().isEmpty()) {
            searchDto.setSortField("create_date");
        }
        
        if (searchDto.getSortDirection() == null || 
            (!searchDto.getSortDirection().equalsIgnoreCase("ASC") && 
             !searchDto.getSortDirection().equalsIgnoreCase("DESC"))) {
            searchDto.setSortDirection("DESC");
        }
        
        if (searchDto.getBaseCondition() == null || searchDto.getBaseCondition().trim().isEmpty()) {
            searchDto.setBaseCondition("delete_yn = 'N'");
        }
    }
}
