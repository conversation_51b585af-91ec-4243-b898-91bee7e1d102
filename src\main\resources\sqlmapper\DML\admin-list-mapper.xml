<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.common.mapper.AdminListMapper">

    <!-- 공통 동적 검색 조건 SQL Fragment -->
    <sql id="commonSearchConditions">
        <where>
            <!-- 기본 조건 (delete_yn = 'N' 등) -->
            <if test="baseCondition != null and baseCondition != ''">
                ${baseCondition}
            </if>
            
            <!-- 검색 키워드 조건 -->
            <if test="hasSearchKeyword() and searchFields != null and !searchFields.isEmpty()">
                AND (
                <foreach collection="searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchKeyword}, '%')
                </foreach>
                )
            </if>
            
            <!-- 필터 조건들 -->
            <if test="hasFilters()">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
            
            <!-- 날짜 범위 조건 -->
            <if test="hasDateRange()">
                AND ${dateField} BETWEEN #{dateFrom} AND #{dateTo}
            </if>
            
            <!-- 숫자 범위 조건 -->
            <if test="hasNumberRange()">
                AND ${numberField} BETWEEN #{numberFrom} AND #{numberTo}
            </if>
        </where>
    </sql>
    
    <!-- 공통 정렬 조건 SQL Fragment -->
    <sql id="commonOrderBy">
        <if test="hasValidSort()">
            ORDER BY ${sortField} ${sortDirection}
        </if>
        <if test="!hasValidSort()">
            ORDER BY create_date DESC
        </if>
    </sql>
    
    <!-- 공통 페이징 조건 SQL Fragment -->
    <sql id="commonPaging">
        LIMIT #{offset}, #{limit}
    </sql>
    
    <!-- 동적 리스트 조회 템플릿 -->
    <select id="selectListWithConditions" parameterType="AdminListSearch" resultType="map">
        SELECT *
        FROM ${tableName}
        <include refid="commonSearchConditions"/>
        <include refid="commonOrderBy"/>
        <include refid="commonPaging"/>
    </select>
    
    <!-- 동적 개수 조회 템플릿 -->
    <select id="countWithConditions" parameterType="AdminListSearch" resultType="long">
        SELECT COUNT(*)
        FROM ${tableName}
        <include refid="commonSearchConditions"/>
    </select>
    
    <!-- 특정 테이블용 동적 리스트 조회 (테이블명을 파라미터로 받지 않는 버전) -->
    <select id="selectListWithConditionsForTable" parameterType="map" resultType="map">
        SELECT *
        FROM ${tableName}
        <where>
            <!-- 기본 조건 -->
            <if test="searchCondition.baseCondition != null and searchCondition.baseCondition != ''">
                ${searchCondition.baseCondition}
            </if>
            
            <!-- 검색 키워드 조건 -->
            <if test="searchCondition.hasSearchKeyword() and searchCondition.searchFields != null and !searchCondition.searchFields.isEmpty()">
                AND (
                <foreach collection="searchCondition.searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchCondition.searchKeyword}, '%')
                </foreach>
                )
            </if>
            
            <!-- 필터 조건들 -->
            <if test="searchCondition.hasFilters()">
                <foreach collection="searchCondition.filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
            
            <!-- 날짜 범위 조건 -->
            <if test="searchCondition.hasDateRange()">
                AND ${searchCondition.dateField} BETWEEN #{searchCondition.dateFrom} AND #{searchCondition.dateTo}
            </if>
            
            <!-- 숫자 범위 조건 -->
            <if test="searchCondition.hasNumberRange()">
                AND ${searchCondition.numberField} BETWEEN #{searchCondition.numberFrom} AND #{searchCondition.numberTo}
            </if>
        </where>
        <if test="searchCondition.hasValidSort()">
            ORDER BY ${searchCondition.sortField} ${searchCondition.sortDirection}
        </if>
        <if test="!searchCondition.hasValidSort()">
            ORDER BY create_date DESC
        </if>
        LIMIT #{searchCondition.offset}, #{searchCondition.limit}
    </select>
    
    <!-- 특정 테이블용 동적 개수 조회 -->
    <select id="countWithConditionsForTable" parameterType="map" resultType="long">
        SELECT COUNT(*)
        FROM ${tableName}
        <where>
            <!-- 기본 조건 -->
            <if test="searchCondition.baseCondition != null and searchCondition.baseCondition != ''">
                ${searchCondition.baseCondition}
            </if>
            
            <!-- 검색 키워드 조건 -->
            <if test="searchCondition.hasSearchKeyword() and searchCondition.searchFields != null and !searchCondition.searchFields.isEmpty()">
                AND (
                <foreach collection="searchCondition.searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchCondition.searchKeyword}, '%')
                </foreach>
                )
            </if>
            
            <!-- 필터 조건들 -->
            <if test="searchCondition.hasFilters()">
                <foreach collection="searchCondition.filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
            
            <!-- 날짜 범위 조건 -->
            <if test="searchCondition.hasDateRange()">
                AND ${searchCondition.dateField} BETWEEN #{searchCondition.dateFrom} AND #{searchCondition.dateTo}
            </if>
            
            <!-- 숫자 범위 조건 -->
            <if test="searchCondition.hasNumberRange()">
                AND ${searchCondition.numberField} BETWEEN #{searchCondition.numberFrom} AND #{searchCondition.numberTo}
            </if>
        </where>
    </select>

</mapper>
