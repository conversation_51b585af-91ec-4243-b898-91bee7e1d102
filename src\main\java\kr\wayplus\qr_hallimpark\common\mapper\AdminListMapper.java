package kr.wayplus.qr_hallimpark.common.mapper;

import kr.wayplus.qr_hallimpark.common.dto.AdminListSearchDto;
import org.apache.ibatis.annotations.Mapper;

import java.util.List;

/**
 * 관리자 리스트 페이지 공통 매퍼 인터페이스
 * - 동적 쿼리를 통한 범용적인 검색/필터/정렬/페이징 기능 제공
 * - 각 도메인별 매퍼에서 상속하여 사용
 */
@Mapper
public interface AdminListMapper<T> {
    
    /**
     * 동적 조건에 따른 리스트 조회
     * @param searchDto 검색 조건
     * @return 조건에 맞는 데이터 목록
     */
    List<T> selectListWithConditions(AdminListSearchDto searchDto);
    
    /**
     * 동적 조건에 따른 전체 개수 조회
     * @param searchDto 검색 조건
     * @return 조건에 맞는 전체 데이터 개수
     */
    Long countWithConditions(AdminListSearchDto searchDto);
}
