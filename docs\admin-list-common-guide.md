# 관리자 리스트 페이지 공통 기능 사용 가이드

## 📋 개요

이 가이드는 관리자 페이지의 리스트 기능을 표준화하고 재사용 가능하게 만든 공통 컴포넌트의 사용법을 설명합니다.

### 🎯 주요 기능
- **통합 검색**: 여러 필드에 대한 키워드 검색
- **동적 필터링**: 설정 기반 필터 옵션
- **유연한 정렬**: 다양한 정렬 기준 지원
- **페이징**: 성능 최적화된 페이징
- **AJAX 기반**: 페이지 새로고침 없는 부드러운 UX
- **반응형 디자인**: 모바일/태블릿 완벽 지원

## 🏗️ 아키텍처

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend       │    │   Database      │
│                 │    │                 │    │                 │
│ AdminListManager│◄──►│BaseAdminList    │◄──►│ Dynamic Query   │
│ (JavaScript)    │    │Service          │    │ (MyBatis)       │
│                 │    │                 │    │                 │
│ admin-list-     │    │ AdminListMapper │    │ admin-list-     │
│ controls.html   │    │                 │    │ mapper.xml      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## 🚀 빠른 시작

### 1. 새로운 도메인에 적용하기

#### 1.1 Mapper 확장
```java
@Mapper
@Repository
public interface YourDomainMapper extends AdminListMapper<YourDomain> {
    // 기존 CRUD 메서드들...
    List<YourDomain> selectYourDomainList();
    YourDomain selectYourDomainById(Long id);
    // ...
}
```

#### 1.2 Service 확장
```java
@Service
@RequiredArgsConstructor
@Transactional(readOnly = true)
public class YourDomainService extends BaseAdminListService<YourDomain> {
    private final YourDomainMapper yourDomainMapper;
    
    @Override
    protected AdminListMapper<YourDomain> getMapper() {
        return yourDomainMapper;
    }
    
    @Override
    protected String getTableName() {
        return "your_table_name";
    }
    
    @Override
    protected String getDefaultSortField() {
        return "create_date"; // 또는 적절한 기본 정렬 필드
    }
    
    @Override
    protected void validateDomainSpecificConditions(AdminListSearch searchCondition) {
        // 허용된 검색 필드 검증
        List<String> allowedSearchFields = Arrays.asList("field1", "field2", "field3");
        validateSearchFields(searchCondition, allowedSearchFields);
        
        // 허용된 정렬 필드 검증
        List<String> allowedSortFields = Arrays.asList("id", "name", "create_date");
        validateSortField(searchCondition, allowedSortFields);
    }
    
    // 기존 CRUD 메서드들은 그대로 유지...
}
```

#### 1.3 MyBatis XML 확장
```xml
<mapper namespace="your.package.YourDomainMapper">
    <!-- 기존 쿼리들... -->
    
    <!-- 공통 동적 검색 조건을 사용한 리스트 조회 -->
    <select id="selectListWithConditions" parameterType="AdminListSearch" resultMap="YourDomainResultMap">
        SELECT *
        FROM your_table_name
        <where>
            <!-- 기본 조건 -->
            <if test="baseCondition != null and baseCondition != ''">
                ${baseCondition}
            </if>
            
            <!-- 검색 키워드 조건 -->
            <if test="hasSearchKeyword() and searchFields != null and !searchFields.isEmpty()">
                AND (
                <foreach collection="searchFields" item="field" separator=" OR ">
                    ${field} LIKE CONCAT('%', #{searchKeyword}, '%')
                </foreach>
                )
            </if>
            
            <!-- 필터 조건들 -->
            <if test="hasFilters()">
                <foreach collection="filters" index="key" item="value">
                    <if test="value != null and value != ''">
                        AND ${key} = #{value}
                    </if>
                </foreach>
            </if>
            
            <!-- 날짜 범위 조건 -->
            <if test="hasDateRange()">
                AND ${dateField} BETWEEN #{dateFrom} AND #{dateTo}
            </if>
        </where>
        <if test="hasValidSort()">
            ORDER BY ${sortField} ${sortDirection}
        </if>
        <if test="!hasValidSort()">
            ORDER BY create_date DESC
        </if>
        LIMIT #{offset}, #{limit}
    </select>
    
    <!-- 공통 동적 검색 조건을 사용한 개수 조회 -->
    <select id="countWithConditions" parameterType="AdminListSearch" resultType="long">
        SELECT COUNT(*)
        FROM your_table_name
        <where>
            <!-- 위와 동일한 조건들 (ORDER BY, LIMIT 제외) -->
        </where>
    </select>
</mapper>
```

#### 1.4 Controller 구현
```java
@Controller
@RequestMapping("/manage/your-domain")
@RequiredArgsConstructor
public class ManageYourDomainController {
    private final YourDomainService yourDomainService;
    
    /**
     * 리스트 페이지
     */
    @GetMapping({"", "/"})
    public ModelAndView list(HttpServletRequest request) {
        ModelAndView modelAndView = new ModelAndView("manage/your-domain/list");
        
        // URL 파라미터에서 검색 조건 생성
        AdminListSearch searchCondition = AdminListUtils.createSearchConditionFromParameters(request.getParameterMap());
        
        // 기본 검색 필드 설정
        if (searchCondition.getSearchFields() == null || searchCondition.getSearchFields().isEmpty()) {
            searchCondition.setSearchFields(Arrays.asList("field1", "field2"));
        }
        
        modelAndView.addObject("searchCondition", searchCondition);
        return modelAndView;
    }
    
    /**
     * AJAX 검색 엔드포인트
     */
    @PostMapping("/search")
    @ResponseBody
    public AdminListResponse<YourDomain> search(@RequestBody AdminListSearch searchCondition) {
        try {
            // 검색 필드 설정
            if (searchCondition.getSearchFields() == null || searchCondition.getSearchFields().isEmpty()) {
                searchCondition.setSearchFields(Arrays.asList("field1", "field2"));
            }
            
            return yourDomainService.findListWithConditions(searchCondition);
            
        } catch (Exception e) {
            return AdminListResponse.error("검색 중 오류가 발생했습니다: " + e.getMessage(), "SEARCH_ERROR");
        }
    }
}
```

### 2. HTML 템플릿 작성

#### 2.1 기본 템플릿 구조
```html
<!DOCTYPE html>
<html lang="ko" xmlns:th="http://www.thymeleaf.org" xmlns:layout="http://www.ultraq.net.nz/thymeleaf/layout"
      layout:decorate="~{layout/manage}">
<head>
    <title>도메인 관리 - 한림공원 관리시스템</title>
</head>
<th:block layout:fragment="head">
    <link href="/css/manage/common/admin-list.css" rel="stylesheet">
</th:block>
<body>
    <div layout:fragment="content">
        <!-- 페이지 헤더 -->
        <section class="page-header">
            <div class="container">
                <div class="row">
                    <div class="col-md-8">
                        <h2>도메인 관리</h2>
                        <p>도메인을 관리합니다.</p>
                    </div>
                    <div class="col-md-4">
                        <a th:href="@{/manage/your-domain/new}" class="btn btn-primary">
                            새 항목 등록
                        </a>
                    </div>
                </div>
            </div>
        </section>

        <!-- 메인 콘텐츠 -->
        <section>
            <div class="container">
                <!-- 공통 검색/필터/정렬 컨트롤 -->
                <div th:replace="fragments/admin-list-controls :: controls(
                    searchFields='field1:필드1,field2:필드2',
                    filterOptions='status:상태:전체,ACTIVE:활성,INACTIVE:비활성',
                    sortOptions='id:ID,name:이름,create_date:등록일',
                    searchPlaceholder='검색어를 입력하세요',
                    showDateRange=false,
                    showNumberRange=false
                )"></div>
                
                <!-- 동적 테이블 컨테이너 -->
                <div th:replace="fragments/admin-list-controls :: table-container"></div>
                
                <!-- 페이징 네비게이션 -->
                <div th:replace="fragments/admin-list-controls :: pagination(${listResponse})"></div>
            </div>
        </section>
    </div>

    <!-- JavaScript -->
    <th:block layout:fragment="script">
        <script src="/js/manage/admin-list-manager.js"></script>
        <script th:inline="javascript">
            const pageConfig = {
                apiEndpoint: '/manage/your-domain/search',
                customRowRenderer: function(items, response) {
                    // 커스텀 테이블 렌더링 로직
                    return renderYourDomainTable(items);
                }
            };
            
            const listManager = new AdminListManager(pageConfig);
            
            function renderYourDomainTable(items) {
                // 테이블 HTML 생성 로직
            }
        </script>
    </th:block>
</body>
</html>
```

## 📚 상세 설정 가이드

### 검색 필드 설정
```
searchFields='field1:라벨1,field2:라벨2,field3:라벨3'
```

### 필터 옵션 설정
```
filterOptions='field1:라벨1:전체,VALUE1:옵션1,VALUE2:옵션2|field2:라벨2:전체,VALUE3:옵션3'
```

### 정렬 옵션 설정
```
sortOptions='field1:라벨1,field2:라벨2,field3:라벨3'
```

### 날짜/숫자 범위 검색
```html
showDateRange=true
showNumberRange=true
```

## 🔧 고급 사용법

### 커스텀 렌더러 구현
```javascript
const pageConfig = {
    customRowRenderer: function(items, response) {
        if (!items || items.length === 0) {
            return this.renderEmptyResult();
        }
        
        let html = '<table class="table table-hover"><thead>...</thead><tbody>';
        items.forEach(item => {
            html += `<tr>
                <td>${item.field1}</td>
                <td>${item.field2}</td>
                <td>
                    <button onclick="editItem(${item.id})">수정</button>
                    <button onclick="deleteItem(${item.id})">삭제</button>
                </td>
            </tr>`;
        });
        html += '</tbody></table>';
        
        return html;
    }
};
```

### 콜백 함수 활용
```javascript
const pageConfig = {
    onDataLoaded: function(response) {
        console.log('데이터 로드 완료:', response);
        // 추가 처리 로직
    },
    onError: function(error) {
        console.error('오류 발생:', error);
        // 에러 처리 로직
    },
    onSearchStart: function(condition) {
        console.log('검색 시작:', condition);
        // 로딩 표시 등
    }
};
```

## 🎨 CSS 커스터마이징

### 기본 스타일 오버라이드
```css
/* 페이지별 커스텀 스타일 */
.admin-list-controls {
    /* 검색 컨트롤 영역 커스터마이징 */
}

.admin-list-table-container {
    /* 테이블 영역 커스터마이징 */
}

.admin-list-pagination {
    /* 페이징 영역 커스터마이징 */
}
```

## 🐛 문제 해결

### 자주 발생하는 문제들

1. **검색이 작동하지 않는 경우**
   - MyBatis XML에 `selectListWithConditions`, `countWithConditions` 쿼리가 있는지 확인
   - 검색 필드명이 데이터베이스 컬럼명과 일치하는지 확인

2. **정렬이 작동하지 않는 경우**
   - `validateDomainSpecificConditions`에서 허용된 정렬 필드를 확인
   - SQL Injection 방지를 위해 허용된 필드만 사용

3. **페이징이 이상한 경우**
   - `countWithConditions` 쿼리가 올바른 개수를 반환하는지 확인
   - LIMIT, OFFSET 계산이 정확한지 확인

## 📖 예제 코드

완전한 예제는 `QuizCategoryService`, `ManageQuizCategoryController`, `list-new.html`을 참고하세요.

## 🔄 마이그레이션 가이드

기존 리스트 페이지를 공통 기능으로 마이그레이션하는 단계:

1. **Mapper 확장**: `AdminListMapper` 상속 추가
2. **Service 확장**: `BaseAdminListService` 상속 추가
3. **XML 쿼리 추가**: 동적 검색 쿼리 추가
4. **Controller API 추가**: 검색 엔드포인트 추가
5. **템플릿 생성**: 공통 컴포넌트 사용한 새 템플릿
6. **테스트**: 모든 기능 정상 동작 확인
7. **기존 페이지 교체**: 검증 완료 후 교체

이 가이드를 따라하면 일관되고 재사용 가능한 관리자 리스트 페이지를 쉽게 구현할 수 있습니다.

---

## 📋 API 명세서

### 검색 API

**Endpoint:** `POST /manage/{domain}/search`

**Request Body:**
```json
{
  "searchKeyword": "검색어",
  "searchFields": ["field1", "field2"],
  "filters": {
    "status": "ACTIVE",
    "category": "TYPE1"
  },
  "dateFrom": "2024-01-01",
  "dateTo": "2024-12-31",
  "dateField": "create_date",
  "numberFrom": 10,
  "numberTo": 100,
  "numberField": "view_count",
  "sortField": "create_date",
  "sortDirection": "DESC",
  "page": 1,
  "size": 20
}
```

**Response:**
```json
{
  "success": true,
  "message": "조회가 완료되었습니다.",
  "items": [...],
  "currentPage": 1,
  "pageSize": 20,
  "totalCount": 150,
  "totalPages": 8,
  "hasPrevious": false,
  "hasNext": true,
  "isFirst": true,
  "isLast": false,
  "isSearchResult": true,
  "searchCondition": {...}
}
```

### 주요 클래스 구조

#### AdminListSearch
```java
public class AdminListSearch {
    private String searchKeyword;           // 검색 키워드
    private List<String> searchFields;     // 검색 대상 필드
    private Map<String, String> filters;   // 필터 조건
    private LocalDate dateFrom, dateTo;    // 날짜 범위
    private String dateField;              // 날짜 필드명
    private Integer numberFrom, numberTo;  // 숫자 범위
    private String numberField;            // 숫자 필드명
    private String sortField;              // 정렬 필드
    private String sortDirection;          // 정렬 방향
    private Integer page, size;            // 페이징
    private String tableName;              // 테이블명
    private String baseCondition;          // 기본 조건
}
```

#### AdminListResponse
```java
public class AdminListResponse<T> {
    private List<T> items;                 // 데이터 목록
    private Integer currentPage;           // 현재 페이지
    private Integer pageSize;              // 페이지 크기
    private Long totalCount;               // 전체 개수
    private Integer totalPages;            // 전체 페이지 수
    private Boolean hasPrevious, hasNext;  // 이전/다음 페이지 존재 여부
    private Boolean isFirst, isLast;       // 첫/마지막 페이지 여부
    private AdminListSearch searchCondition; // 검색 조건
    private Boolean isSearchResult;        // 검색 결과 여부
    private Boolean success;               // 성공 여부
    private String message;                // 메시지
    private String errorCode;              // 에러 코드
}
```
