package kr.wayplus.qr_hallimpark.common.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

/**
 * 관리자 리스트 페이지 공통 응답 DTO
 * - 페이징된 리스트 데이터와 메타 정보를 포함
 * - AJAX 응답 및 Thymeleaf 템플릿에서 사용
 */
@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdminListResponseDto<T> {

    // ========== 데이터 ==========
    
    /**
     * 리스트 데이터
     * - 현재 페이지의 실제 데이터 목록
     */
    private List<T> items;
    
    // ========== 페이징 메타 정보 ==========
    
    /**
     * 현재 페이지 번호 (1부터 시작)
     */
    private Integer currentPage;
    
    /**
     * 페이지당 표시할 항목 수
     */
    private Integer pageSize;
    
    /**
     * 전체 데이터 개수
     */
    private Long totalCount;
    
    /**
     * 전체 페이지 수
     */
    private Integer totalPages;
    
    /**
     * 이전 페이지 존재 여부
     */
    private Boolean hasPrevious;
    
    /**
     * 다음 페이지 존재 여부
     */
    private Boolean hasNext;
    
    /**
     * 첫 번째 페이지 여부
     */
    private Boolean isFirst;
    
    /**
     * 마지막 페이지 여부
     */
    private Boolean isLast;
    
    // ========== 검색/필터 메타 정보 ==========
    
    /**
     * 적용된 검색 조건
     */
    private AdminListSearchDto searchCondition;
    
    /**
     * 검색 결과 여부
     * - 검색 조건이 적용된 결과인지 확인
     */
    private Boolean isSearchResult;
    
    // ========== 응답 상태 ==========
    
    /**
     * 성공 여부
     */
    @Builder.Default
    private Boolean success = true;
    
    /**
     * 메시지
     */
    private String message;
    
    /**
     * 에러 코드 (실패 시)
     */
    private String errorCode;
    
    // ========== 유틸리티 메서드 ==========
    
    /**
     * 페이징 정보 계산 및 설정
     * @param searchDto 검색 조건
     * @param totalCount 전체 데이터 개수
     */
    public void calculatePagingInfo(AdminListSearchDto searchDto, Long totalCount) {
        this.currentPage = searchDto.getPage();
        this.pageSize = searchDto.getSize();
        this.totalCount = totalCount;
        this.totalPages = (int) Math.ceil((double) totalCount / pageSize);
        this.hasPrevious = currentPage > 1;
        this.hasNext = currentPage < totalPages;
        this.isFirst = currentPage == 1;
        this.isLast = currentPage.equals(totalPages) || totalPages == 0;
        this.searchCondition = searchDto;
        this.isSearchResult = searchDto.hasSearchKeyword() || searchDto.hasFilters() || 
                             searchDto.hasDateRange() || searchDto.hasNumberRange();
    }
    
    /**
     * 빈 결과 생성
     * @param searchDto 검색 조건
     * @return 빈 결과 응답
     */
    public static <T> AdminListResponseDto<T> empty(AdminListSearchDto searchDto) {
        AdminListResponseDto<T> response = AdminListResponseDto.<T>builder()
                .items(List.of())
                .success(true)
                .message("조회된 데이터가 없습니다.")
                .build();
        response.calculatePagingInfo(searchDto, 0L);
        return response;
    }
    
    /**
     * 성공 결과 생성
     * @param items 데이터 목록
     * @param searchDto 검색 조건
     * @param totalCount 전체 데이터 개수
     * @return 성공 응답
     */
    public static <T> AdminListResponseDto<T> success(List<T> items, AdminListSearchDto searchDto, Long totalCount) {
        AdminListResponseDto<T> response = AdminListResponseDto.<T>builder()
                .items(items)
                .success(true)
                .message("조회가 완료되었습니다.")
                .build();
        response.calculatePagingInfo(searchDto, totalCount);
        return response;
    }
    
    /**
     * 실패 결과 생성
     * @param errorMessage 에러 메시지
     * @param errorCode 에러 코드
     * @return 실패 응답
     */
    public static <T> AdminListResponseDto<T> error(String errorMessage, String errorCode) {
        return AdminListResponseDto.<T>builder()
                .items(List.of())
                .success(false)
                .message(errorMessage)
                .errorCode(errorCode)
                .totalCount(0L)
                .totalPages(0)
                .currentPage(1)
                .pageSize(20)
                .hasPrevious(false)
                .hasNext(false)
                .isFirst(true)
                .isLast(true)
                .isSearchResult(false)
                .build();
    }
    
    /**
     * 페이지 번호 목록 생성 (페이징 네비게이션용)
     * @param displayPageCount 표시할 페이지 번호 개수
     * @return 페이지 번호 목록
     */
    public List<Integer> getPageNumbers(int displayPageCount) {
        if (totalPages == 0) {
            return List.of();
        }
        
        int startPage = Math.max(1, currentPage - displayPageCount / 2);
        int endPage = Math.min(totalPages, startPage + displayPageCount - 1);
        
        // 끝 페이지가 조정되었을 때 시작 페이지도 다시 조정
        if (endPage - startPage + 1 < displayPageCount) {
            startPage = Math.max(1, endPage - displayPageCount + 1);
        }
        
        return java.util.stream.IntStream.rangeClosed(startPage, endPage)
                .boxed()
                .collect(java.util.stream.Collectors.toList());
    }
    
    /**
     * 현재 페이지의 시작 항목 번호 (전체 기준)
     */
    public Long getStartItemNumber() {
        if (totalCount == 0) {
            return 0L;
        }
        return totalCount - ((long) (currentPage - 1) * pageSize);
    }
    
    /**
     * 현재 페이지의 종료 항목 번호 (전체 기준)
     */
    public Long getEndItemNumber() {
        if (totalCount == 0) {
            return 0L;
        }
        return Math.max(0L, getStartItemNumber() - items.size() + 1);
    }
}
