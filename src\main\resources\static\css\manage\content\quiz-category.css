/* 한림공원 QR 체험 - 퀴즈 카테고리 관리 스타일 */

/* 테이블 스타일 */
.table {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
    padding: 1rem;
}

.table tbody tr {
    transition: background-color 0.2s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa;
}

.table tbody td {
    padding: 1rem;
    vertical-align: middle;
    border-bottom: 1px solid #e9ecef;
}

/* 배지 스타일 */
.badge {
    font-size: 0.875rem;
    padding: 0.5rem 0.75rem;
}

/* 버튼 그룹 */
.btn-group .btn {
    margin: 0;
}

.btn-sm {
    padding: 0.375rem 0.75rem;
    font-size: 0.875rem;
    border-radius: 0.375rem;
}

/* 테이블 반응형 */
.table-responsive {
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

/* 폼 컨테이너 */
.form-container {
    max-width: 800px;
    margin: 0 auto;
}

/* 폼 카드 */
.form-card {
    background: white;
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
}

/* 폼 헤더 */
.form-header {
    background: var(--bg-gradient-primary);
    color: white;
    padding: var(--spacing-xl);
    text-align: center;
}

.form-title {
    font-size: 1.5rem;
    font-weight: 600;
    margin: 0;
}

.form-subtitle {
    margin-top: var(--spacing-sm);
    opacity: 0.9;
}

/* 폼 본문 */
.form-body {
    padding: var(--spacing-xl);
}

/* 폼 그룹 */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    font-weight: 600;
    color: var(--admin-secondary);
    margin-bottom: var(--spacing-sm);
}

.form-control {
    border-radius: var(--border-radius-sm);
    border: 2px solid #e2e8f0;
    padding: 0.75rem;
    transition: all var(--transition-base);
}

.form-control:focus {
    border-color: var(--primary-color);
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

/* 텍스트 에어리어 */
.form-textarea {
    min-height: 120px;
    resize: vertical;
}

/* 파일 업로드 */
.file-upload-area {
    border: 2px dashed #cbd5e0;
    border-radius: 10px;
    padding: 2rem;
    text-align: center;
    transition: all 0.3s ease;
    cursor: pointer;
}

.file-upload-area:hover {
    border-color: #2E8B57;
    background-color: rgba(46, 139, 87, 0.05);
}

.file-upload-icon {
    font-size: 3rem;
    color: #a0aec0;
    margin-bottom: 1rem;
}

.file-upload-text {
    color: #4a5568;
    font-weight: 500;
}

.file-upload-hint {
    color: #718096;
    font-size: 0.875rem;
    margin-top: 0.5rem;
}

/* 이미지 미리보기 */
.image-preview {
    max-width: 200px;
    border-radius: 10px;
    box-shadow: 0 4px 8px rgba(0,0,0,0.1);
    margin-top: 1rem;
}

/* 폼 액션 */
.form-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    padding-top: 2rem;
    border-top: 1px solid #e2e8f0;
}

/* 검색 및 필터 */
.search-filter-bar {
    background: white;
    border-radius: 15px;
    padding: 1.5rem;
    margin-bottom: 2rem;
    box-shadow: 0 2px 8px rgba(0,0,0,0.05);
}

.search-input {
    border-radius: 25px;
    border: 2px solid #e2e8f0;
    padding: 0.75rem 1.5rem;
}

.search-input:focus {
    border-color: #2E8B57;
    box-shadow: 0 0 0 0.2rem rgba(46, 139, 87, 0.25);
}

/* 반응형 디자인 */
@media (max-width: 768px) {
    .form-actions {
        flex-direction: column;
    }

    .form-container {
        margin: 0 1rem;
    }

    .table-responsive {
        font-size: 0.875rem;
    }

    .btn-group {
        flex-direction: column;
    }

    .btn-group .btn {
        margin-bottom: 0.25rem;
    }
}

/* 빈 상태 */
.empty-state {
    text-align: center;
    padding: 3rem;
    color: #6c757d;
}

.empty-state i {
    font-size: 4rem;
    margin-bottom: 1rem;
    opacity: 0.5;
}

/* 그라데이션 배경 */
.bg-gradient-primary {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
}

/* 제출 버튼 */
.btn-submit {
    background: linear-gradient(135deg, #2E8B57, #20B2AA);
    border: none;
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
    transition: transform 0.3s ease;
}

.btn-submit:hover {
    transform: translateY(-2px);
}

/* 취소 버튼 */
.btn-cancel {
    border-radius: 10px;
    padding: 0.75rem 2rem;
    font-weight: 600;
}

/* 카테고리 미리보기 */
.category-preview {
    background: #f8f9fa;
    border-radius: 10px;
    padding: 1rem;
    margin-top: 1rem;
}

/* 필수 표시 */
.required {
    color: #dc3545;
}
