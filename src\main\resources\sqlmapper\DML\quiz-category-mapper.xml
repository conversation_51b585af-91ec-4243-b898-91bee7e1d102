<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">

<mapper namespace="kr.wayplus.qr_hallimpark.mapper.QuizCategoryMapper">

    <!-- 문제 카테고리 ResultMap -->
    <resultMap id="QuizCategoryResultMap" type="kr.wayplus.qr_hallimpark.model.QuizCategory">
        <id property="categoryId" column="category_id"/>
        <result property="categoryName" column="category_name"/>
        <result property="description" column="description"/>
        <result property="createId" column="create_id"/>
        <result property="createDate" column="create_date"/>
        <result property="lastUpdateId" column="last_update_id"/>
        <result property="lastUpdateDate" column="last_update_date"/>
        <result property="deleteYn" column="delete_yn"/>
        <result property="deleteId" column="delete_id"/>
        <result property="deleteDate" column="delete_date"/>
    </resultMap>

    <!-- 모든 문제 카테고리 목록 조회 -->
    <select id="selectQuizCategoryList" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE delete_yn = 'N'
        ORDER BY category_id ASC
    </select>

    <!-- 카테고리 ID로 문제 카테고리 조회 -->
    <select id="selectQuizCategoryById" parameterType="long" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE category_id = #{categoryId}
        AND delete_yn = 'N'
    </select>

    <!-- 카테고리명으로 문제 카테고리 조회 -->
    <select id="selectQuizCategoryByName" parameterType="string" resultMap="QuizCategoryResultMap">
        SELECT
            category_id,
            category_name,
            description,
            create_id,
            create_date,
            last_update_id,
            last_update_date,
            delete_yn,
            delete_id,
            delete_date
        FROM quiz_category
        WHERE category_name = #{categoryName}
        AND delete_yn = 'N'
    </select>

    <!-- 문제 카테고리 등록 -->
    <insert id="insertQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory" useGeneratedKeys="true" keyProperty="categoryId">
        INSERT INTO quiz_category (
            category_name,
            description,
            create_id,
            delete_yn
        ) VALUES (
            #{categoryName},
            #{description},
            #{createId},
            'N'
        )
    </insert>

    <!-- 문제 카테고리 수정 -->
    <update id="updateQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory">
        UPDATE quiz_category
        SET
            category_name = #{categoryName},
            description = #{description},
            last_update_id = #{lastUpdateId}
        WHERE category_id = #{categoryId}
        AND delete_yn = 'N'
    </update>

    <!-- 문제 카테고리 삭제 (소프트 삭제) -->
    <update id="deleteQuizCategory" parameterType="kr.wayplus.qr_hallimpark.model.QuizCategory">
        UPDATE quiz_category
        SET
            category_name = CONCAT(category_name, '_DELETED_', UNIX_TIMESTAMP()),
            delete_yn = 'Y',
            delete_id = #{deleteId},
            delete_date = NOW()
        WHERE category_id = #{categoryId}
        AND delete_yn = 'N'
    </update>

    <!-- 카테고리명 중복 체크 -->
    <select id="countByCategoryName" parameterType="string" resultType="int">
        SELECT COUNT(*)
        FROM quiz_category
        WHERE category_name = #{categoryName}
        AND delete_yn = 'N'
    </select>

    <!-- 카테고리명 중복 체크 (수정 시 자기 자신 제외) -->
    <select id="countByCategoryNameExcludeId" resultType="int">
        SELECT COUNT(*)
        FROM quiz_category
        WHERE category_name = #{categoryName}
        AND category_id != #{categoryId}
        AND delete_yn = 'N'
    </select>

</mapper>
